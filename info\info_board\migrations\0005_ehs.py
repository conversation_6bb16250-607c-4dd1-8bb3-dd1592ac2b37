# Generated by Django 5.1.4 on 2025-06-11 06:36

import info_board.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('info_board', '0004_announcement_attachment_file'),
    ]

    operations = [
        migrations.CreateModel(
            name='EHS',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('date_posted', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.<PERSON><PERSON>anField(default=True)),
                ('background_image', models.ImageField(blank=True, null=True, upload_to='ehs/')),
                ('attachment_file', models.FileField(blank=True, help_text='Upload PowerPoint (.ppt, .pptx) or PDF (.pdf) files', null=True, upload_to='ehs/attachments/', validators=[info_board.models.validate_presentation_file])),
            ],
            options={
                'verbose_name': 'EHS',
                'verbose_name_plural': 'EHS',
            },
        ),
    ]
