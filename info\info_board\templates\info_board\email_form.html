{% extends 'info_board/base.html' %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card" style="background-color: rgba(51, 51, 51, 0.7); backdrop-filter: blur(10px);">
                <div class="card-body text-white">
                    <h2 class="card-title mb-4"><PERSON><PERSON></h2>

                    <!-- Messages Display -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show message-alert" role="alert">
                                {% if message.tags == 'success' %}
                                    <i class="fas fa-check-circle me-2"></i>
                                {% elif message.tags == 'error' %}
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                {% elif message.tags == 'warning' %}
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                {% else %}
                                    <i class="fas fa-info-circle me-2"></i>
                                {% endif %}
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        <div class="mb-4">
                            <label class="form-label">{{ form.sender_name.label }}</label>
                            {{ form.sender_name.errors }}
                            <input type="text" name="sender_name" class="form-control bg-dark text-white" style="height: 45px;" required>
                        </div>
                        <div class="mb-4">
                            <label class="form-label">{{ form.sender_email.label }}</label>
                            {{ form.sender_email.errors }}
                            <input type="email" name="sender_email" class="form-control bg-dark text-white" style="height: 45px;" required>
                        </div>
                        <div class="mb-4">
                            <label class="form-label">{{ form.recipient_email.label }}</label>
                            {{ form.recipient_email.errors }}
                            <input type="email" name="recipient_email" class="form-control bg-dark text-white" style="height: 45px;" required>
                        </div>
                        <div class="mb-4">
                            <label class="form-label">{{ form.content.label }}</label>
                            {{ form.content.errors }}
                            <textarea name="content" class="form-control bg-dark text-white" style="min-height: 200px;" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary px-4 py-2">Küldés</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .form-control {
        border: 1px solid #444;
        background-color: rgba(33, 33, 33, 0.7) !important;
        font-size: 16px; /* Prevents zoom on iOS */
        min-height: 44px; /* Touch-friendly */
    }
    .form-control:focus {
        background-color: rgba(51, 51, 51, 0.9) !important;
        color: white;
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* Mobile optimizations for email form */
    @media (max-width: 768px) {
        .col-md-8 {
            padding: 0 10px;
        }

        .card {
            margin: 10px 0;
            border-radius: 12px;
        }

        .card-body {
            padding: 20px 15px;
        }

        .form-control {
            padding: 12px 15px;
            border-radius: 8px;
        }

        textarea.form-control {
            min-height: 150px !important;
            resize: vertical;
        }

        .btn {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            border-radius: 8px;
        }

        .form-label {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .mb-4 {
            margin-bottom: 1.5rem !important;
        }
    }

    @media (max-width: 576px) {
        .container {
            padding: 0 5px;
        }

        .card-body {
            padding: 15px 10px;
        }

        .card-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        textarea.form-control {
            min-height: 120px !important;
        }
    }

    /* Message Alert Styles */
    .message-alert {
        border: none;
        border-radius: 10px;
        margin-bottom: 20px;
        font-weight: 500;
        animation: slideDown 0.5s ease-out;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .alert-success {
        background: linear-gradient(135deg, #28a745, #20c997) !important;
        color: white !important;
        border-left: 4px solid #1e7e34;
    }

    .alert-error,
    .alert-danger {
        background: linear-gradient(135deg, #dc3545, #c82333) !important;
        color: white !important;
        border-left: 4px solid #bd2130;
    }

    .alert-warning {
        background: linear-gradient(135deg, #ffc107, #e0a800) !important;
        color: #212529 !important;
        border-left: 4px solid #d39e00;
    }

    .alert-info {
        background: linear-gradient(135deg, #17a2b8, #138496) !important;
        color: white !important;
        border-left: 4px solid #117a8b;
    }

    .btn-close {
        filter: invert(1);
        opacity: 0.8;
    }

    .btn-close:hover {
        opacity: 1;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Auto-hide messages after 5 seconds */
    .message-alert {
        transition: opacity 0.5s ease-out;
    }

    /* Enhanced mobile responsiveness for alerts */
    @media (max-width: 768px) {
        .message-alert {
            font-size: 0.9rem;
            padding: 12px 15px;
        }

        .message-alert i {
            font-size: 1rem;
        }
    }
</style>

<script>
    // Auto-hide success messages after 5 seconds
    document.addEventListener('DOMContentLoaded', function() {
        const successAlerts = document.querySelectorAll('.alert-success');
        successAlerts.forEach(function(alert) {
            setTimeout(function() {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000); // 5 seconds
        });

        // Add smooth fade-out animation
        const allAlerts = document.querySelectorAll('.message-alert');
        allAlerts.forEach(function(alert) {
            const closeBtn = alert.querySelector('.btn-close');
            if (closeBtn) {
                closeBtn.addEventListener('click', function() {
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.remove();
                    }, 500);
                });
            }
        });

        // Prevent zoom on double tap for iOS
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);

        // Form validation feedback
        const form = document.querySelector('form');
        const submitBtn = document.querySelector('button[type="submit"]');

        if (form && submitBtn) {
            form.addEventListener('submit', function() {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Küldés...';
                submitBtn.disabled = true;
            });
        }
    });
</script>

{% endblock %}