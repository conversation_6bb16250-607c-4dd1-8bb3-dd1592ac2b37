@echo off
echo ========================================
echo   InfoTable Windows Service Creator
echo ========================================
echo.

REM Check for admin privileges
net session >nul 2>&1
if %errorLevel% NEQ 0 (
    echo ❌ This script requires Administrator privileges.
    echo    Please run as Administrator.
    pause
    exit /b 1
)

echo ✓ Administrator privileges confirmed
echo.

REM Get current directory
set CURRENT_DIR=%CD%
set PROJECT_DIR=%CURRENT_DIR%\info
set PYTHON_PATH=%CURRENT_DIR%\venv\Scripts\python.exe

REM Check if virtual environment exists
if not exist "%PYTHON_PATH%" (
    set PYTHON_PATH=python
    echo ⚠ Virtual environment not found, using system Python
) else (
    echo ✓ Using virtual environment Python
)

echo Current directory: %CURRENT_DIR%
echo Project directory: %PROJECT_DIR%
echo Python path: %PYTHON_PATH%
echo.

REM Create service script
echo Creating service script...
(
echo @echo off
echo cd /d "%PROJECT_DIR%"
echo "%PYTHON_PATH%" manage.py runserver 0.0.0.0:8000
) > "%CURRENT_DIR%\start-infotable-service.bat"

echo ✓ Service script created: start-infotable-service.bat
echo.

REM Create NSSM service (if NSSM is available)
where nssm >nul 2>&1
if %errorLevel% EQU 0 (
    echo NSSM found, creating Windows service...
    nssm install InfoTableService "%CURRENT_DIR%\start-infotable-service.bat"
    nssm set InfoTableService DisplayName "InfoTable Web Application"
    nssm set InfoTableService Description "Django-based InfoTable application for company information"
    nssm set InfoTableService Start SERVICE_AUTO_START
    
    echo ✓ Windows service 'InfoTableService' created successfully!
    echo.
    echo Service commands:
    echo   Start:   net start InfoTableService
    echo   Stop:    net stop InfoTableService
    echo   Status:  sc query InfoTableService
    echo.
    
    set /p start_now="Start the service now? (y/n): "
    if /i "%start_now%"=="y" (
        net start InfoTableService
        echo ✓ Service started successfully!
    )
) else (
    echo NSSM not found. Installing NSSM...
    echo.
    echo Please download NSSM from: https://nssm.cc/download
    echo Or use the manual service creation below.
    echo.
    
    echo Manual Service Creation:
    echo -----------------------
    echo 1. Download NSSM from https://nssm.cc/download
    echo 2. Extract nssm.exe to a folder in your PATH
    echo 3. Run this script again
    echo.
    echo Alternative: Task Scheduler Setup
    echo ---------------------------------
    echo 1. Open Task Scheduler
    echo 2. Create Basic Task
    echo 3. Name: InfoTable Service
    echo 4. Trigger: When computer starts
    echo 5. Action: Start a program
    echo 6. Program: %CURRENT_DIR%\start-infotable-service.bat
    echo 7. Check "Run with highest privileges"
)

echo.
echo ========================================
echo   Service Setup Complete
echo ========================================
echo.
echo Your InfoTable will be accessible at:
echo   http://%COMPUTERNAME%:8000
echo   http://localhost:8000
echo.
echo To access from other devices on the network:
echo   http://%COMPUTERNAME%:8000
echo.
pause
