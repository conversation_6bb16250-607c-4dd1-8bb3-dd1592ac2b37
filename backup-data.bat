@echo off
echo ========================================
echo   InfoTable Data Backup Script
echo ========================================
echo.

REM Create backup directory with timestamp
set TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set BACKUP_DIR=backup_%TIMESTAMP%

echo Creating backup directory: %BACKUP_DIR%
mkdir "%BACKUP_DIR%"

REM Backup database
echo.
echo Backing up database...
if exist "info\db.sqlite3" (
    copy "info\db.sqlite3" "%BACKUP_DIR%\db.sqlite3"
    echo ✓ Database backed up successfully
) else (
    echo ⚠ Database file not found at info\db.sqlite3
)

REM Backup media files
echo.
echo Backing up media files...
if exist "info\media" (
    xcopy "info\media" "%BACKUP_DIR%\media" /E /I /H
    echo ✓ Media files backed up successfully
) else (
    echo ⚠ Media directory not found at info\media
)

REM Backup static files (if any custom ones exist)
echo.
echo Backing up static files...
if exist "info\static" (
    xcopy "info\static" "%BACKUP_DIR%\static" /E /I /H
    echo ✓ Static files backed up successfully
) else (
    echo ℹ No custom static files found
)

REM Create a backup info file
echo.
echo Creating backup information file...
echo Backup created on: %date% %time% > "%BACKUP_DIR%\backup_info.txt"
echo Original location: %CD% >> "%BACKUP_DIR%\backup_info.txt"
echo Database: db.sqlite3 >> "%BACKUP_DIR%\backup_info.txt"
echo Media files: media\ directory >> "%BACKUP_DIR%\backup_info.txt"

echo.
echo ========================================
echo   Backup completed successfully!
echo   Backup location: %BACKUP_DIR%
echo ========================================
echo.
echo IMPORTANT: Keep this backup safe before updating your project!
echo.
pause
