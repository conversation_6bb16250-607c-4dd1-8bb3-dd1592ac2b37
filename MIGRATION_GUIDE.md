# InfoTable Project Migration Guide

This guide will help you update your InfoTable project on another PC while preserving all existing content (announcements, EHS entries, images, admin users, etc.).

## 🎯 Overview

The new version includes:
- ✅ EHS page with green hover effects
- ✅ Enhanced email confirmation messages
- ✅ Fixed mobile dropdown menu
- ✅ Improved file management and cleanup

## 📋 Migration Steps

### Step 1: Backup Current Data (On Target PC)

1. **Navigate to your current project directory**
2. **Run the backup script:**
   ```cmd
   backup-data.bat
   ```
   This creates a timestamped backup of your database and media files.

### Step 2: Export Data for Migration (On Target PC)

1. **Run the export script:**
   ```cmd
   export-data.bat
   ```
   This creates a `data_export` folder with:
   - `all_data.json` - Complete database export
   - `info_board_data.json` - App-specific data
   - `users.json` - Admin user accounts
   - `media/` - All uploaded files
   - `IMPORT_INSTRUCTIONS.txt` - Import guide

### Step 3: Transfer Files

1. **Copy the `data_export` folder** to your other PC
2. **Copy the updated project code** to the other PC
3. **Place both in the same directory**

### Step 4: Import Data (On Target PC)

1. **Navigate to the updated project directory**
2. **Run the import script:**
   ```cmd
   import-data.bat
   ```
   This will:
   - Run database migrations
   - Import all your content
   - Copy media files
   - Verify the setup

### Step 5: Verify Migration

1. **Start the server:**
   ```cmd
   cd info
   python manage.py runserver
   ```

2. **Check these areas:**
   - ✅ Home page loads correctly
   - ✅ Announcements page shows existing content
   - ✅ New EHS page is available (with green hover effects)
   - ✅ Email page shows confirmation messages
   - ✅ Mobile dropdown menu works properly
   - ✅ Admin panel shows all content
   - ✅ Images and attachments display correctly

## 🔧 Manual Alternative (If Scripts Fail)

### Manual Backup:
```cmd
# Copy database
copy "info\db.sqlite3" "backup_db.sqlite3"

# Copy media files
xcopy "info\media" "backup_media" /E /I /H
```

### Manual Export:
```cmd
cd info
python manage.py dumpdata --indent 2 > data_backup.json
cd ..
```

### Manual Import:
```cmd
cd info
python manage.py migrate
python manage.py loaddata data_backup.json
cd ..
xcopy "backup_media" "info\media" /E /I /H /Y
```

## 🚨 Troubleshooting

### If Import Fails:
1. **Check migration status:**
   ```cmd
   cd info
   python manage.py showmigrations
   ```

2. **Run migrations manually:**
   ```cmd
   python manage.py migrate
   ```

3. **Create superuser if needed:**
   ```cmd
   python manage.py createsuperuser
   ```

### If Media Files Don't Show:
1. **Check media directory structure:**
   ```
   info/
   ├── media/
   │   ├── announcements/
   │   ├── ehs/
   │   └── ...
   ```

2. **Verify file permissions**
3. **Check Django settings for MEDIA_URL and MEDIA_ROOT**

### If EHS Page Shows Errors:
1. **Run migrations specifically:**
   ```cmd
   python manage.py migrate info_board
   ```

2. **Check if EHS model exists:**
   ```cmd
   python manage.py shell
   >>> from info_board.models import EHS
   >>> EHS.objects.count()
   ```

## 📞 Support

If you encounter issues:
1. **Check the backup files** are intact
2. **Verify Python/Django versions** match
3. **Run Django's built-in checks:**
   ```cmd
   python manage.py check
   ```

## ✅ Success Checklist

After migration, verify:
- [ ] All announcements are visible
- [ ] All EHS entries are accessible (if any existed)
- [ ] Images and attachments work
- [ ] Admin users can log in
- [ ] Email functionality works with confirmations
- [ ] Mobile dropdown menu functions properly
- [ ] New EHS page has green hover effects

## 🎉 New Features Available

After successful migration, you'll have:
1. **EHS Page** - Same functionality as announcements with green theme
2. **Email Confirmations** - Success/failure messages for email sending
3. **Fixed Mobile Menu** - Dropdown works properly on mobile devices
4. **Enhanced File Management** - Better cleanup and organization

---

**Important:** Always keep your backup files until you've verified everything works correctly!
