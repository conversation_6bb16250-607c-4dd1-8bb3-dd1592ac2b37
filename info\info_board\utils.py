import os
import sys
from django.conf import settings
from django.core.files.base import ContentFile
import logging

logger = logging.getLogger(__name__)


def convert_ppt_to_pdf(ppt_file_path, output_dir=None):
    """
    Convert PowerPoint file to PDF using Windows COM interface.
    Returns the path to the generated PDF file or None if conversion fails.
    """
    if not os.path.exists(ppt_file_path):
        logger.error(f"PowerPoint file not found: {ppt_file_path}")
        return None

    if output_dir is None:
        output_dir = os.path.dirname(ppt_file_path)

    # Generate PDF filename
    base_name = os.path.splitext(os.path.basename(ppt_file_path))[0]
    pdf_path = os.path.join(output_dir, f"{base_name}.pdf")

    # If PDF already exists and is newer than PPT, return it
    if os.path.exists(pdf_path):
        ppt_mtime = os.path.getmtime(ppt_file_path)
        pdf_mtime = os.path.getmtime(pdf_path)
        if pdf_mtime > ppt_mtime:
            logger.info(f"PDF already exists and is up to date: {pdf_path}")
            return pdf_path

    try:
        # Try using COM interface (Windows only)
        if sys.platform == "win32":
            return _convert_with_com(ppt_file_path, pdf_path)
        else:
            # For non-Windows systems, try LibreOffice
            return _convert_with_libreoffice(ppt_file_path, pdf_path)
    except Exception as e:
        logger.error(f"Failed to convert PowerPoint to PDF: {e}")
        return None


def _convert_with_com(ppt_file_path, pdf_path):
    """Convert using Windows COM interface (PowerPoint application)."""
    try:
        import comtypes.client

        # Create PowerPoint application
        powerpoint = comtypes.client.CreateObject("Powerpoint.Application")
        powerpoint.Visible = 1

        # Open the presentation
        presentation = powerpoint.Presentations.Open(ppt_file_path)

        # Export as PDF
        # Format: 32 = PDF format
        presentation.SaveAs(pdf_path, 32)

        # Close presentation and quit PowerPoint
        presentation.Close()
        powerpoint.Quit()

        if os.path.exists(pdf_path):
            logger.info(
                f"Successfully converted PowerPoint to PDF: {pdf_path}")
            return pdf_path
        else:
            logger.error("PDF file was not created")
            return None

    except Exception as e:
        logger.error(f"COM conversion failed: {e}")
        return None


def _convert_with_libreoffice(ppt_file_path, pdf_path):
    """Convert using LibreOffice headless mode (cross-platform)."""
    try:
        import subprocess

        output_dir = os.path.dirname(pdf_path)

        # LibreOffice command
        cmd = [
            'libreoffice',
            '--headless',
            '--convert-to', 'pdf',
            '--outdir', output_dir,
            ppt_file_path
        ]

        result = subprocess.run(
            cmd, capture_output=True, text=True, timeout=60)

        if result.returncode == 0 and os.path.exists(pdf_path):
            logger.info(
                f"Successfully converted PowerPoint to PDF using LibreOffice: {pdf_path}")
            return pdf_path
        else:
            logger.error(f"LibreOffice conversion failed: {result.stderr}")
            return None

    except subprocess.TimeoutExpired:
        logger.error("LibreOffice conversion timed out")
        return None
    except FileNotFoundError:
        logger.error("LibreOffice not found. Please install LibreOffice.")
        return None
    except Exception as e:
        logger.error(f"LibreOffice conversion failed: {e}")
        return None


def get_pdf_path_for_announcement(announcement):
    """
    Get the PDF path for an announcement's PowerPoint attachment.
    Converts the PowerPoint to PDF if needed.
    """
    if not announcement.has_powerpoint_attachment():
        return None

    ppt_path = announcement.attachment_file.path
    pdf_dir = os.path.join(settings.MEDIA_ROOT, 'announcements', 'pdf')

    # Create PDF directory if it doesn't exist
    os.makedirs(pdf_dir, exist_ok=True)

    # Convert PowerPoint to PDF
    pdf_path = convert_ppt_to_pdf(ppt_path, pdf_dir)

    if pdf_path:
        # Return relative path from MEDIA_ROOT
        relative_path = os.path.relpath(pdf_path, settings.MEDIA_ROOT)
        return relative_path.replace('\\', '/')  # Use forward slashes for URLs

    return None


def get_pdf_url_for_announcement(announcement, request):
    """Get the full URL for the PDF version of an announcement's PowerPoint."""
    pdf_path = get_pdf_path_for_announcement(announcement)
    if pdf_path:
        return request.build_absolute_uri(f"{settings.MEDIA_URL}{pdf_path}")
    return None


def get_pdf_path_for_ehs(ehs):
    """
    Get the PDF path for an EHS's PowerPoint attachment.
    Converts the PowerPoint to PDF if needed.
    """
    if not ehs.has_powerpoint_attachment:
        return None

    ppt_path = ehs.attachment_file.path
    pdf_dir = os.path.join(settings.MEDIA_ROOT, 'ehs', 'pdf')

    # Create PDF directory if it doesn't exist
    os.makedirs(pdf_dir, exist_ok=True)

    # Convert PowerPoint to PDF
    pdf_path = convert_ppt_to_pdf(ppt_path, pdf_dir)

    if pdf_path:
        # Return relative path from MEDIA_ROOT
        relative_path = os.path.relpath(pdf_path, settings.MEDIA_ROOT)
        return relative_path.replace('\\', '/')  # Use forward slashes for URLs

    return None


def get_pdf_url_for_ehs(ehs, request):
    """Get the full URL for the PDF version of an EHS's PowerPoint."""
    pdf_path = get_pdf_path_for_ehs(ehs)
    if pdf_path:
        return request.build_absolute_uri(f"{settings.MEDIA_URL}{pdf_path}")
    return None
