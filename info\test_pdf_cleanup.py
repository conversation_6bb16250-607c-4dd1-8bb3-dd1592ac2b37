#!/usr/bin/env python
"""
Test script to verify PDF cleanup functionality.
This script can be run to test if PDF files are properly deleted.
"""

import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'info.settings')
django.setup()

from info_board.models import Announcement
from django.conf import settings

def list_pdf_files():
    """List all PDF files in the announcements/pdf directory."""
    pdf_dir = os.path.join(settings.MEDIA_ROOT, 'announcements', 'pdf')
    if os.path.exists(pdf_dir):
        pdf_files = [f for f in os.listdir(pdf_dir) if f.endswith('.pdf')]
        print(f"PDF files in {pdf_dir}:")
        for pdf_file in pdf_files:
            print(f"  - {pdf_file}")
        return pdf_files
    else:
        print(f"PDF directory does not exist: {pdf_dir}")
        return []

def test_pdf_cleanup():
    """Test the PDF cleanup functionality."""
    print("=== PDF Cleanup Test ===")
    
    # List current announcements
    announcements = Announcement.objects.all()
    print(f"\nCurrent announcements: {announcements.count()}")
    
    for announcement in announcements:
        print(f"  - ID: {announcement.id}, Title: {announcement.title}")
        if announcement.has_presentation_attachment():
            print(f"    Has attachment: {announcement.attachment_file.name}")
            print(f"    File type: {'PDF' if announcement.has_pdf_attachment() else 'PowerPoint'}")
            
            # Check if PDF exists
            pdf_path = announcement.get_pdf_path()
            if pdf_path:
                full_pdf_path = os.path.join(settings.MEDIA_ROOT, pdf_path)
                exists = os.path.exists(full_pdf_path)
                print(f"    PDF exists: {exists} ({full_pdf_path})")
    
    print("\n=== Current PDF Files ===")
    pdf_files = list_pdf_files()
    
    print(f"\nTotal PDF files: {len(pdf_files)}")
    
    return pdf_files

if __name__ == "__main__":
    test_pdf_cleanup()
