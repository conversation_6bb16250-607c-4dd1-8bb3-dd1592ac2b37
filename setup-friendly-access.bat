@echo off
echo ========================================
echo   InfoTable Friendly Access Setup
echo ========================================
echo.

REM Get computer information
echo Computer Information:
echo ----------------------
echo Computer Name: %COMPUTERNAME%
echo User Name: %USERNAME%
echo.

REM Get IP address
echo Network Information:
echo --------------------
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set IP=%%a
    set IP=!IP: =!
    echo IP Address: !IP!
)
echo.

REM Create hosts file entry suggestion
echo ========================================
echo   Setup Options for User-Friendly Access
echo ========================================
echo.
echo Option 1: Access by Computer Name
echo ---------------------------------
echo You can access your InfoTable at:
echo   http://%COMPUTERNAME%:8000
echo.
echo From other devices on the same network, use:
echo   http://%COMPUTERNAME%.local:8000  (Mac/Linux)
echo   http://%COMPUTERNAME%:8000        (Windows)
echo.

echo Option 2: Custom Local Domain
echo -----------------------------
echo You can create a custom address like: http://infotable.local:8000
echo.
echo To set this up:
echo 1. Run this script as Administrator
echo 2. Choose option 2 when prompted
echo.

echo Option 3: Port 80 (No Port Number)
echo ----------------------------------
echo Access without :8000 by running on port 80
echo Note: Requires Administrator privileges
echo.

echo ========================================
echo   Choose Setup Option
echo ========================================
echo.
echo 1. Test computer name access (recommended)
echo 2. Setup custom local domain (requires admin)
echo 3. Setup port 80 access (requires admin)
echo 4. Show current access methods
echo 5. Exit
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto test_computer_name
if "%choice%"=="2" goto setup_custom_domain
if "%choice%"=="3" goto setup_port_80
if "%choice%"=="4" goto show_access_methods
if "%choice%"=="5" goto end

:test_computer_name
echo.
echo Testing computer name access...
echo.
echo Starting Django server on all interfaces...
echo You should be able to access InfoTable at:
echo   http://%COMPUTERNAME%:8000
echo   http://localhost:8000
echo.
echo Press Ctrl+C to stop the server when done testing.
echo.
cd info
python manage.py runserver 0.0.0.0:8000
goto end

:setup_custom_domain
echo.
echo Setting up custom local domain...
echo.
net session >nul 2>&1
if %errorLevel% NEQ 0 (
    echo ❌ This option requires Administrator privileges.
    echo    Please run this script as Administrator.
    pause
    goto end
)

echo Adding 'infotable.local' to hosts file...
echo 127.0.0.1 infotable.local >> C:\Windows\System32\drivers\etc\hosts
echo ✓ Custom domain added successfully!
echo.
echo You can now access InfoTable at:
echo   http://infotable.local:8000
echo.
echo Starting server...
cd info
python manage.py runserver 0.0.0.0:8000
goto end

:setup_port_80
echo.
echo Setting up port 80 access...
echo.
net session >nul 2>&1
if %errorLevel% NEQ 0 (
    echo ❌ This option requires Administrator privileges.
    echo    Please run this script as Administrator.
    pause
    goto end
)

echo Starting Django server on port 80...
echo You can now access InfoTable at:
echo   http://%COMPUTERNAME%
echo   http://localhost
echo.
echo Note: Port 80 may conflict with other web servers (IIS, Apache, etc.)
echo.
cd info
python manage.py runserver 0.0.0.0:80
goto end

:show_access_methods
echo.
echo ========================================
echo   Current Access Methods
echo ========================================
echo.
echo Local Access:
echo   http://localhost:8000
echo   http://127.0.0.1:8000
echo.
echo Network Access:
echo   http://%COMPUTERNAME%:8000
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set IP=%%a
    set IP=!IP: =!
    echo   http://!IP!:8000
)
echo.
echo Custom Domains (if configured):
findstr "infotable.local" C:\Windows\System32\drivers\etc\hosts >nul 2>&1
if %errorLevel% EQU 0 (
    echo   http://infotable.local:8000 ✓
) else (
    echo   http://infotable.local:8000 (not configured)
)
echo.
pause
goto end

:end
echo.
echo ========================================
echo   Setup Complete
echo ========================================
echo.
echo For permanent setup, consider:
echo 1. Creating a Windows service
echo 2. Using a reverse proxy (nginx)
echo 3. Setting up automatic startup
echo.
pause
