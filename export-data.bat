@echo off
echo ========================================
echo   InfoTable Data Export Script
echo ========================================
echo.

REM Create export directory
set EXPORT_DIR=data_export
echo Creating export directory: %EXPORT_DIR%
mkdir "%EXPORT_DIR%" 2>nul

REM Navigate to Django project directory
cd info

echo.
echo Exporting database data to JSON...

REM Export all data to JSON fixtures
python manage.py dumpdata --indent 2 --output "..\%EXPORT_DIR%\all_data.json"
if %ERRORLEVEL% EQU 0 (
    echo ✓ All data exported to all_data.json
) else (
    echo ❌ Failed to export all data
    goto :error
)

REM Export specific app data
python manage.py dumpdata info_board --indent 2 --output "..\%EXPORT_DIR%\info_board_data.json"
if %ERRORLEVEL% EQU 0 (
    echo ✓ Info board data exported to info_board_data.json
) else (
    echo ❌ Failed to export info board data
)

REM Export user data (admin accounts)
python manage.py dumpdata auth.user --indent 2 --output "..\%EXPORT_DIR%\users.json"
if %ERRORLEVEL% EQU 0 (
    echo ✓ User accounts exported to users.json
) else (
    echo ❌ Failed to export user data
)

cd ..

REM Copy media files
echo.
echo Copying media files...
if exist "info\media" (
    xcopy "info\media" "%EXPORT_DIR%\media" /E /I /H /Y
    echo ✓ Media files copied successfully
) else (
    echo ⚠ No media files found
)

REM Create import instructions
echo.
echo Creating import instructions...
(
echo ========================================
echo   Data Import Instructions
echo ========================================
echo.
echo After updating your project code, run these commands:
echo.
echo 1. Navigate to the info directory:
echo    cd info
echo.
echo 2. Run migrations:
echo    python manage.py migrate
echo.
echo 3. Import the data:
echo    python manage.py loaddata ..\data_export\all_data.json
echo.
echo 4. Copy media files:
echo    xcopy "..\data_export\media" "media" /E /I /H /Y
echo.
echo 5. Create superuser if needed:
echo    python manage.py createsuperuser
echo.
echo ========================================
) > "%EXPORT_DIR%\IMPORT_INSTRUCTIONS.txt"

echo.
echo ========================================
echo   Export completed successfully!
echo   Export location: %EXPORT_DIR%
echo ========================================
echo.
echo Next steps:
echo 1. Copy the '%EXPORT_DIR%' folder to your other PC
echo 2. Update your project code
echo 3. Follow instructions in IMPORT_INSTRUCTIONS.txt
echo.
pause
goto :end

:error
echo.
echo ❌ Export failed! Make sure you're in the correct directory
echo    and that Python/Django is properly set up.
pause

:end
