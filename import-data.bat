@echo off
echo ========================================
echo   InfoTable Data Import Script
echo ========================================
echo.

REM Check if data export directory exists
if not exist "data_export" (
    echo ❌ Error: data_export directory not found!
    echo    Please make sure you copied the data_export folder from your old PC.
    pause
    exit /b 1
)

echo Found data export directory ✓
echo.

REM Navigate to Django project directory
if not exist "info" (
    echo ❌ Error: info directory not found!
    echo    Please make sure you're in the correct project directory.
    pause
    exit /b 1
)

cd info

echo Checking Django setup...
python manage.py check
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Django check failed! Please fix any issues before importing data.
    pause
    exit /b 1
)

echo.
echo ========================================
echo   Step 1: Running Migrations
echo ========================================
python manage.py migrate
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Migration failed!
    pause
    exit /b 1
)
echo ✓ Migrations completed successfully

echo.
echo ========================================
echo   Step 2: Importing Data
echo ========================================

REM Try to import all data first
if exist "..\data_export\all_data.json" (
    echo Importing all data...
    python manage.py loaddata "..\data_export\all_data.json"
    if %ERRORLEVEL% EQU 0 (
        echo ✓ All data imported successfully
        set DATA_IMPORTED=1
    ) else (
        echo ⚠ Failed to import all data, trying individual imports...
        set DATA_IMPORTED=0
    )
) else (
    echo ⚠ all_data.json not found, trying individual imports...
    set DATA_IMPORTED=0
)

REM If all data import failed, try individual imports
if %DATA_IMPORTED% EQU 0 (
    if exist "..\data_export\users.json" (
        echo Importing users...
        python manage.py loaddata "..\data_export\users.json"
        if %ERRORLEVEL% EQU 0 (
            echo ✓ Users imported successfully
        ) else (
            echo ⚠ Failed to import users
        )
    )
    
    if exist "..\data_export\info_board_data.json" (
        echo Importing info board data...
        python manage.py loaddata "..\data_export\info_board_data.json"
        if %ERRORLEVEL% EQU 0 (
            echo ✓ Info board data imported successfully
        ) else (
            echo ⚠ Failed to import info board data
        )
    )
)

echo.
echo ========================================
echo   Step 3: Copying Media Files
echo ========================================

if exist "..\data_export\media" (
    echo Copying media files...
    xcopy "..\data_export\media" "media" /E /I /H /Y
    if %ERRORLEVEL% EQU 0 (
        echo ✓ Media files copied successfully
    ) else (
        echo ⚠ Some media files may not have been copied
    )
) else (
    echo ℹ No media files to copy
)

cd ..

echo.
echo ========================================
echo   Import Process Completed!
echo ========================================
echo.
echo Your data has been imported into the updated project.
echo.
echo Next steps:
echo 1. Test your application: python manage.py runserver
echo 2. Check admin panel: http://localhost:8000/admin/
echo 3. Verify all content is present
echo.
echo If you encounter any issues:
echo - Check the Django admin for missing content
echo - Verify media files are in the correct location
echo - Create a new superuser if needed: python manage.py createsuperuser
echo.
pause
