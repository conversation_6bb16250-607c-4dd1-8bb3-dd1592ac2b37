# InfoTable User-Friendly Access Guide

Stop typing IP addresses! Here are several ways to access your InfoTable application with user-friendly addresses.

## 🚀 Quick Setup (Recommended)

### Option 1: Computer Name Access
**Easiest and most reliable method**

1. **Run the setup script:**
   ```cmd
   setup-friendly-access.bat
   ```
   Choose option 1 to test computer name access.

2. **Access your InfoTable at:**
   - `http://YOUR-COMPUTER-NAME:8000`
   - Example: `http://OFFICE-PC:8000`

3. **Share with colleagues:**
   - Give them: `http://YOUR-COMPUTER-NAME:8000`
   - Works from any device on the same network

### Option 2: Desktop Shortcuts
**One-click access**

1. **Run the shortcuts creator:**
   ```cmd
   create-shortcuts.bat
   ```

2. **Use the desktop shortcuts:**
   - 🌐 **"Open InfoTable"** - Opens the application
   - ⚡ **"Start InfoTable Server"** - Starts the server
   - 🔧 **"InfoTable Admin"** - Opens admin panel

## 🌐 Access Methods Available

### Local Access (Same Computer)
- `http://localhost:8000` ✅
- `http://127.0.0.1:8000` ✅

### Network Access (Other Devices)
- `http://YOUR-COMPUTER-NAME:8000` ✅
- `http://YOUR-COMPUTER-NAME.local:8000` (Mac/Linux) ✅

### Custom Domain (Optional)
- `http://infotable.local:8000` (requires setup)

### Port 80 Access (No Port Number)
- `http://YOUR-COMPUTER-NAME` (requires admin setup)

## 📱 Mobile & Tablet Access

All addresses work on mobile devices:
- **Phones:** Use the same URLs
- **Tablets:** Fully responsive design
- **Touch-friendly:** Optimized for touch interaction

## 🔧 Advanced Setup Options

### Option A: Windows Service (Always Running)
```cmd
create-service.bat
```
**Benefits:**
- ✅ Starts automatically with Windows
- ✅ Runs in background
- ✅ No need to manually start server

### Option B: Custom Local Domain
```cmd
setup-friendly-access.bat
```
Choose option 2 (requires admin privileges)
**Result:** Access at `http://infotable.local:8000`

### Option C: Port 80 (No Port Number)
```cmd
setup-friendly-access.bat
```
Choose option 3 (requires admin privileges)
**Result:** Access at `http://YOUR-COMPUTER-NAME`

## 🔥 Firewall Setup

The scripts automatically configure Windows Firewall, but if you have issues:

### Manual Firewall Setup:
1. **Open Windows Firewall**
2. **Click "Allow an app or feature"**
3. **Click "Change settings" → "Allow another app"**
4. **Browse to your Python installation**
5. **Check both "Private" and "Public" networks**

### Alternative Method:
```cmd
netsh advfirewall firewall add rule name="InfoTable" dir=in action=allow protocol=TCP localport=8000
```

## 📋 Page URLs Reference

Once set up, access these pages:

| Page | URL |
|------|-----|
| **Home** | `http://YOUR-COMPUTER-NAME:8000/` |
| **Announcements** | `http://YOUR-COMPUTER-NAME:8000/announcements/` |
| **EHS** | `http://YOUR-COMPUTER-NAME:8000/ehs/` |
| **Email** | `http://YOUR-COMPUTER-NAME:8000/email/` |
| **Admin** | `http://YOUR-COMPUTER-NAME:8000/admin/` |

## 🤝 Sharing with Colleagues

### Easy Sharing:
1. **Find your computer name:** Run `hostname` in command prompt
2. **Share this address:** `http://YOUR-COMPUTER-NAME:8000`
3. **Make sure server is running**

### QR Code Sharing (Optional):
- Use any QR code generator
- Create QR code for: `http://YOUR-COMPUTER-NAME:8000`
- Print and post in office

## 🔍 Troubleshooting

### "Site can't be reached" from other devices:
1. ✅ **Check server is running**
2. ✅ **Verify firewall settings**
3. ✅ **Confirm same network**
4. ✅ **Try IP address as backup**

### Computer name doesn't work:
1. **Use IP address temporarily**
2. **Check network discovery settings**
3. **Try with `.local` suffix (Mac/Linux)**

### Port 8000 conflicts:
1. **Change port in Django:** `python manage.py runserver 0.0.0.0:8080`
2. **Update firewall rule for new port**
3. **Update shared addresses**

## 🎯 Best Practices

### For Office Environment:
- ✅ Use computer name access
- ✅ Create desktop shortcuts
- ✅ Set up Windows service
- ✅ Share address with team

### For Home Network:
- ✅ Use localhost for personal use
- ✅ Use computer name for family access
- ✅ Consider port 80 for simplicity

### For Presentations:
- ✅ Use custom domain (`infotable.local`)
- ✅ Test access beforehand
- ✅ Have IP address as backup

## 📞 Quick Reference

**Start Server:** Double-click "Start InfoTable Server" shortcut
**Access Application:** Double-click "Open InfoTable" shortcut
**Admin Panel:** Double-click "InfoTable Admin" shortcut
**Share Address:** `http://YOUR-COMPUTER-NAME:8000`

---

**Remember:** Replace `YOUR-COMPUTER-NAME` with your actual computer name (run `hostname` to find it)!
