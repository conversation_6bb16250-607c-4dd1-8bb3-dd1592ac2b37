@echo off
echo ========================================
echo   InfoTable Desktop Shortcuts Creator
echo ========================================
echo.

REM Get computer name and create shortcuts
set COMPUTER_NAME=%COMPUTERNAME%

echo Creating desktop shortcuts...
echo.

REM Create shortcut to open InfoTable in browser
echo Creating "Open InfoTable" shortcut...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Open InfoTable.lnk'); $Shortcut.TargetPath = 'http://localhost:8000'; $Shortcut.IconLocation = 'shell32.dll,13'; $Shortcut.Description = 'Open InfoTable Web Application'; $Shortcut.Save()"

REM Create shortcut to start InfoTable server
echo Creating "Start InfoTable Server" shortcut...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Start InfoTable Server.lnk'); $Shortcut.TargetPath = '%CD%\start-server.bat'; $Shortcut.WorkingDirectory = '%CD%'; $Shortcut.IconLocation = 'shell32.dll,25'; $Shortcut.Description = 'Start InfoTable Django Server'; $Shortcut.Save()"

REM Create shortcut to admin panel
echo Creating "InfoTable Admin" shortcut...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\InfoTable Admin.lnk'); $Shortcut.TargetPath = 'http://localhost:8000/admin/'; $Shortcut.IconLocation = 'shell32.dll,48'; $Shortcut.Description = 'Open InfoTable Admin Panel'; $Shortcut.Save()"

REM Create start server batch file
echo Creating start server script...
(
echo @echo off
echo title InfoTable Server
echo echo ========================================
echo echo   InfoTable Server Starting...
echo echo ========================================
echo echo.
echo echo Server will be accessible at:
echo echo   Local:    http://localhost:8000
echo echo   Network:  http://%COMPUTER_NAME%:8000
echo echo.
echo echo Press Ctrl+C to stop the server
echo echo.
echo cd /d "%CD%\info"
echo python manage.py runserver 0.0.0.0:8000
echo pause
) > start-server.bat

REM Create network access info file
echo Creating network access information...
(
echo ========================================
echo   InfoTable Network Access Information
echo ========================================
echo.
echo Your InfoTable application can be accessed from:
echo.
echo LOCAL ACCESS:
echo   http://localhost:8000
echo   http://127.0.0.1:8000
echo.
echo NETWORK ACCESS ^(from other devices^):
echo   http://%COMPUTER_NAME%:8000
echo   http://%COMPUTER_NAME%.local:8000 ^(Mac/Linux devices^)
echo.
echo PAGES AVAILABLE:
echo   Home Page:        http://localhost:8000/
echo   Announcements:    http://localhost:8000/announcements/
echo   EHS:             http://localhost:8000/ehs/
echo   Email:           http://localhost:8000/email/
echo   Admin Panel:     http://localhost:8000/admin/
echo.
echo MOBILE ACCESS:
echo   All pages are optimized for mobile devices
echo   Use the same URLs on phones and tablets
echo.
echo SHARING WITH COLLEAGUES:
echo   Share this address: http://%COMPUTER_NAME%:8000
echo   Make sure the InfoTable server is running
echo   Ensure firewall allows connections on port 8000
echo.
echo TROUBLESHOOTING:
echo   - If network access doesn't work, check Windows Firewall
echo   - Make sure the server is running ^(use Start InfoTable Server shortcut^)
echo   - Verify other devices are on the same network
echo.
echo Created on: %date% %time%
) > "InfoTable Access Information.txt"

echo.
echo ✓ Desktop shortcuts created:
echo   - Open InfoTable
echo   - Start InfoTable Server  
echo   - InfoTable Admin
echo.
echo ✓ Files created:
echo   - start-server.bat
echo   - InfoTable Access Information.txt
echo.

REM Create firewall rule
echo Setting up Windows Firewall rule...
netsh advfirewall firewall show rule name="InfoTable Django Server" >nul 2>&1
if %errorLevel% NEQ 0 (
    echo Adding firewall rule for port 8000...
    netsh advfirewall firewall add rule name="InfoTable Django Server" dir=in action=allow protocol=TCP localport=8000
    if %errorLevel% EQU 0 (
        echo ✓ Firewall rule added successfully
    ) else (
        echo ⚠ Could not add firewall rule ^(requires admin privileges^)
        echo   You may need to manually allow port 8000 in Windows Firewall
    )
) else (
    echo ✓ Firewall rule already exists
)

echo.
echo ========================================
echo   Setup Complete!
echo ========================================
echo.
echo Quick Start:
echo 1. Double-click "Start InfoTable Server" on your desktop
echo 2. Double-click "Open InfoTable" to access the application
echo.
echo Network Sharing:
echo   Share this address with colleagues: http://%COMPUTER_NAME%:8000
echo.
echo For more information, see: "InfoTable Access Information.txt"
echo.
pause
